// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "./generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String         @id @default(uuid())
  email         String         @unique
  password      String
  name          String
  image         String?
  phone         String?
  streetAddress String?
  postalCode    String?
  city          String?
  country       String?
  role          UserRole       @default(USER)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  orders        OrderProduct[]
}

enum UserRole {
  USER
  ADMIN
}

model Order {
  id            String         @id @default(uuid())
  paid          Boolean        @default(false)
  subTotal      Float
  deliveryFee   Float
  totalPrice    Float
  userEmail     String
  phone         String
  streetAddress String
  postalCode    String
  city          String
  country       String
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  products      OrderProduct[]
}

model OrderProduct {
  id            String         @id @default(uuid())
  quantity      Int
  order         Order          @relation(fields: [orderId], references: [id])
  orderId       String
  user          User?   @relation(fields: [userId], references: [id])
  userId        String?
  product       Product        @relation(fields: [productId], references: [id])
  productId     String
}

model Product {
  id          String         @id @default(cuid())
  name        String
  description String
  image       String
  order       Int            @default(autoincrement())
  basePrice   Float
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  sizes       Size[]
  extras      Extra[]
  orders      OrderProduct[]
  category    Category       @relation(fields: [categoryId], references: [id])
  categoryId  String
}

enum ProductSize {
  SMALL
  MEDIUM
  LARGE
}

model Size{
  id          String         @id @default(cuid())
  name        ProductSize
  Product     Product        @relation(fields: [productId], references: [id])
  productId   String
  price       Float
}

enum ExtraIngredients {
CHEESE
BACON
TOMATOES
ONIONS
PEPPERS
PEPPERONI
SAUSAGE
MUSHROOMS
OLIVES
}

model Extra{
  id          String         @id @default(cuid())
  name        ExtraIngredients
  Product     Product        @relation(fields: [productId], references: [id])
  productId   String
  price       Float
}

model Category {
  id       String    @id @default(uuid())
  name     String
  order    Int       @default(autoincrement())
  products Product[]
}