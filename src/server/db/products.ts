import { unstable_cache } from "next/cache";
import { db } from "@/lib/prisma";

export const getBestSellers = unstable_cache(
    async () => {
        return db.product.findMany({
            take: 3, // Only get first 3 products for the home page
            orderBy: {
                orders: {
                    _count: 'desc'
                }
            },
            include: {
                sizes: true,
                extras: true,
            },
            where: {
                orders: {
                    some: {}
                }
            }
        });
    },
    ['best-sellers'],
    {
        revalidate: 60,
        tags: ['best-sellers']
    }
);